import React, { useState, useEffect, useRef } from "react";
import { StatusBar } from "expo-status-bar";
import { View } from "react-native";
import { enableScreens } from "react-native-screens";
import * as SplashScreen1 from "expo-splash-screen";
import { useFonts } from "expo-font";
import MainStack from "./app/navigation/MainStack";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "./app/RequestHandlers/CredentailsContext";
import i18n, { initLanguage } from "./i18n"; // Import your i18n instance
import { LanguageProvider } from "./app/context/LanguageContext";
import {
  GoogleSignin,
  GoogleSigninButton,
  statusCodes,
} from "@react-native-google-signin/google-signin";
import { SmileID } from "@smile_identity/react-native";
import { FingerPrintStatus } from "./app/context/FingerPrintContext";
import { DepositProvider } from "./app/context/DepositeContext";
import * as Notifications from "expo-notifications";
import { AuthManager } from "./app/Utils/authManager";
import { appStateManager } from "./app/Utils/appStateManager";
import { PushNotificationManager } from "./app/Utils/pushNotificationManager";
import { UpdateUser } from "./app/RequestHandlers/User";
import CustomSplashScreen from "./app/components/CustomSplashScreen";
import OffLineToast from "./app/components/ErrorSate/OfflineComponent";
import { UpdateModal } from "./app/screens/UpdateModal";
import DeviceInfo from "react-native-device-info";
import { DeviceIdContext } from "./app/RequestHandlers/DeviceIdContext";
import { ToastProvider } from "./app/context/ToastContext";
import * as SecureStore from "expo-secure-store";
import * as Updates from "expo-updates";
import * as Application from "expo-application";
import { GlobalModalProvider } from "./app/context/GlobalModalContext";
import { WelcomeModalProvider } from "./app/context/WelcomeModalContext";
import GlobalWelcomeModal from "./app/components/GlobalWelcomeModal";
import * as Linking from "expo-linking";

SplashScreen1.setOptions({
  duration: 1000,
  fade: true,
});
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

// Push notification functions moved to PushNotificationManager

// Silent EAS Update Function
async function checkForEASUpdate() {
  try {
    const update = await Updates.checkForUpdateAsync();
    if (update.isAvailable) {
      await Updates.fetchUpdateAsync();
      await Updates.reloadAsync(); // Restart to apply the update silently
    }
  } catch (error) {
    console.log("EAS Update Error:", error);
  }
}

export default function App() {
  useEffect(() => {
    SmileID.initialize(false);
  }, []);
  GoogleSignin.configure({
    webClientId:
      "532609421408-3m8b2bfi1gmah9nuebe02mtpasa1fc5v.apps.googleusercontent.com",
    iosClientId:
      "532609421408-buqsghmlhuph82b65sipjs745p9mc1h8.apps.googleusercontent.com",
    profileImageSize: 120,
  });
  enableScreens();
  const [appReady, setAppReady] = useState(false);
  const [storedCredentails, setStoredCredentails] = useState<any>(null);
  const [storedFingerPrintStatus, setStoredFingerPrintStatus] = useState("");
  const [storedPrivateKey, setStoredPrivateKey] = useState("");
  const [storedID, setStoredID] = useState("");
  const [storedHasPinStatus, setStoredHasPinStatus] = useState("");
  const [fontsLoaded] = useFonts({
    "poppins-bold": require("./app/assets/fonts/Poppins-Bold.ttf"),
    "poppins-semibold": require("./app/assets/fonts/Poppins-SemiBold.ttf"),
    "poppins-medium": require("./app/assets/fonts/Poppins-Medium.ttf"),
    "poppins-regular": require("./app/assets/fonts/Poppins-Regular.ttf"),
  });
  const [notification, setNotification] = useState<
    Notifications.Notification | undefined
  >(undefined);
  const notificationListener = useRef<Notifications.Subscription>();
  const responseListener = useRef<Notifications.Subscription>();

  useEffect(() => {
    checkForEASUpdate();
  }, []);

  useEffect(() => {
    const fetchUniqueId = async () => {
      const id = await DeviceInfo.getUniqueId();
      setStoredID(id);
      AsyncStorage.setItem("uniqueID", id);
    };

    fetchUniqueId();
    _registerForPushNotifications();
    notificationListener.current =
      Notifications.addNotificationReceivedListener((notification) => {
        setNotification(notification);
      });
    responseListener.current =
      Notifications.addNotificationResponseReceivedListener((response) => {});
    return () => {
      notificationListener.current &&
        Notifications.removeNotificationSubscription(
          notificationListener.current
        );
      responseListener.current &&
        Notifications.removeNotificationSubscription(responseListener.current);
    };
  }, []);
  useEffect(() => {
    if (appReady) {
      SplashScreen1.hideAsync();
    }
  }, [appReady]);

  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen1.hideAsync();
    }
  }, [fontsLoaded]);

  // Deep link handling removed
  const [data, setData] = useState(null);
  function handleDeepLink(event) {
    let data = Linking.parse(event.url);
    setData(data)
  }

  useEffect(() => {
    Linking.addEventListener("url", handleDeepLink);
    return () => {
      Linking.removeEventListener("url");
    };
  }, []);

  async function _registerForPushNotifications() {
    await PushNotificationManager.registerForPushNotifications();
  }
  const clearLogin = async () => {
    await AuthManager.clearLogin(setStoredCredentails);
  };

  // check token expiration
  const parseJwt = (token) => {
    return AuthManager.parseJwt(token, clearLogin);
  };
  // App state management
  useEffect(() => {
    // Configure app state manager
    appStateManager.updateConfig({
      hasPin: storedCredentails?.user?.hasPin || false,
      inactivityThreshold: 300, // 5 minutes
    });

    // Initialize app state monitoring
    appStateManager.initialize();

    return () => {
      appStateManager.cleanup();
    };
  }, [storedCredentails]);

  const checkSession = async () => {
    await AuthManager.checkSession(clearLogin);
  };

  // Deep link test function removed

  // checkLoginCredentails function has been incorporated into initializeApp

  const checkFingerPrintStatus = async (userId: string) => {
    try {
      // Check if secure storage is available
      await SecureStore.isAvailableAsync();

      // Get fingerprint status and private key
      const res = await AsyncStorage.getItem(`fingerPrintStatus${userId}`);
      const privateKey = await SecureStore.getItemAsync(`privateKey${userId}`);

      // Update fingerprint status
      if (res !== null) {
        setStoredFingerPrintStatus(res);
      } else {
        setStoredFingerPrintStatus(null);
      }

      // Update private key
      if (privateKey !== null) {
        setStoredPrivateKey(privateKey);
      } else {
        setStoredPrivateKey(null);
      }
    } catch (error) {
      console.error("Error checking fingerprint status:", error);
    }
  };
  useEffect(() => {
    // clearLogin()
    const interval = setInterval(() => {
      if (storedCredentails != null) {
        parseJwt(storedCredentails);
        checkSession();
      }
    }, 60000);
    return () => clearInterval(interval);
  }, [storedCredentails]);

  const initializeApp = async () => {
    try {
      console.log("Initializing language settings");
      await initLanguage(); // Initialize language settings
      // Convert checkLoginCredentails to a promise
      return new Promise<void>((resolve) => {
        console.log("Checking login credentials");
        // Get login credentials
        setTimeout(async () => {
          try {
            const res = await AsyncStorage.getItem("cookies");
            if (res != null && typeof res === "string") {
              const newRes = JSON.parse(res);
              setStoredCredentails(newRes);
              parseJwt(newRes);
              checkFingerPrintStatus(newRes.user.id);
              checkSession();
            } else {
              setStoredCredentails(null);
            }
            setAppReady(true);
            console.log("Login credentials checked");
            resolve(); // Resolve the promise when done
          } catch (err) {
            console.error("Error checking login credentials:", err);
            setAppReady(true);
            resolve(); // Resolve even on error
          }
        }, 1000); // Reduced timeout to improve performance
      });
    } catch (error) {
      console.error("Error in app initialization:", error);
      setAppReady(true);
    }
  };
  // useEffect(() => {
  //  customFunc()
  // }, []);

  const [isSplashComplete, setIsSplashComplete] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);

  // Handle splash screen completion
  const handleSplashComplete = () => {
    console.log("Splash animation complete");
    setIsSplashComplete(true);
  };

  // Initialize app only once
  useEffect(() => {
    const initialize = async () => {
      if (isInitializing) {
        console.log("Starting app initialization");
        await initializeApp();
        setIsInitializing(false);
        console.log("App initialization complete");
      }
    };

    initialize();
  }, [isInitializing]);

  // Show splash screen during initialization or while fonts are loading
  if (isInitializing || !appReady || !fontsLoaded || !isSplashComplete) {
    return (
      <CustomSplashScreen
        onFinish={handleSplashComplete}
        appIsReady={!isInitializing && appReady && fontsLoaded}
      />
    );
  }

  return (
    <>
      <StatusBar style="auto" translucent={true} />
      <LanguageProvider>
        <CredentailsContext.Provider
          // @ts-ignore
          value={{ storedCredentails, setStoredCredentails }}
        >
          <FingerPrintStatus.Provider
            value={{
              storedFingerPrintStatus,
              //@ts-ignore
              setStoredFingerPrintStatus,
              storedPrivateKey,
              // @ts-ignore
              setStoredPrivateKey,
            }}
          >
            {/* @ts-ignore */}
            <DeviceIdContext.Provider value={{ storedID, setStoredID }}>
              <WelcomeModalProvider>
                <DepositProvider>
                  <ToastProvider>
                    <GestureHandlerRootView style={{ flex: 1 }}>
                      <OffLineToast />
                      <UpdateModal />
                      <GlobalModalProvider>
                        <MainStack />
                      </GlobalModalProvider>
                      <GlobalWelcomeModal />
                    </GestureHandlerRootView>
                  </ToastProvider>
                </DepositProvider>
              </WelcomeModalProvider>
            </DeviceIdContext.Provider>
          </FingerPrintStatus.Provider>
        </CredentailsContext.Provider>
      </LanguageProvider>

      {/* Custom Splash Screen */}
    </>
  );
}

// Removed unused styles
