{"name": "sfx", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/config-plugins": "~9.0.0", "@expo/prebuild-config": "~8.0.0", "@invertase/react-native-apple-authentication": "^2.4.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/analytics": "^21.11.0", "@react-native-firebase/app": "^21.11.0", "@react-native-google-signin/google-signin": "^13.1.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "@smile_identity/react-native": "^10.2.2", "@sumsub/react-native-mobilesdk-module": "^1.35.1", "@types/react": "~18.3.12", "expo": "~52.0.47", "expo-apple-authentication": "~7.1.3", "expo-application": "~6.0.2", "expo-auth-session": "~6.0.3", "expo-build-properties": "^0.13.2", "expo-camera": "~16.0.15", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.3", "expo-crypto": "~14.0.2", "expo-dev-client": "~5.0.11", "expo-device": "~7.0.2", "expo-file-system": "~18.0.11", "expo-font": "~13.0.2", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.5", "expo-linear-gradient": "~14.0.2", "expo-local-authentication": "~15.0.2", "expo-localization": "~16.0.1", "expo-media-library": "~17.0.5", "expo-modules-core": "~2.2.1", "expo-notifications": "~0.29.13", "expo-print": "~14.0.3", "expo-secure-store": "~14.0.1", "expo-sharing": "~13.0.1", "expo-splash-screen": "~0.29.21", "expo-status-bar": "~2.0.1", "expo-updates": "~0.26.19", "firebase": "^10.13.2", "formik": "^2.4.6", "i18n-js": "^4.4.3", "multicoin-address-validator": "^0.5.22", "react": "18.3.1", "react-content-loader": "^7.0.2", "react-native": "0.76.7", "react-native-aes-crypto": "^3.2.1", "react-native-calendars": "^1.1312.0", "react-native-color-matrix-image-filters": "^7.0.1", "react-native-compressor": "^1.10.3", "react-native-country-codes-picker": "^2.3.5", "react-native-date-picker": "^5.0.12", "react-native-device-info": "^14.0.4", "react-native-dotenv": "^3.4.11", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-in-app-review": "^4.3.5", "react-native-modal": "^13.0.1", "react-native-modern-datepicker": "^1.0.0-beta.91", "react-native-pager-view": "6.5.1", "react-native-qrcode-svg": "^6.3.1", "react-native-reanimated": "~3.16.1", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-share": "^10.2.1", "react-native-skeleton-content": "^1.0.28", "react-native-svg": "15.8.0", "react-native-svg-transformer": "^1.5.0", "react-native-tab-view": "^3.5.2", "react-native-view-shot": "~4.0.3", "react-native-webview": "13.12.5", "typescript": "~5.3.3", "updates": "^16.4.0", "uuid": "^11.0.3", "yup": "^1.4.0", "expo-linking": "~7.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react-native": "^0.72.8", "babel-plugin-transform-remove-console": "^6.9.4", "eas-cli": "^10.2.2"}, "private": true}